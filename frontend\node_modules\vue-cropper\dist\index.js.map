{"version": 3, "file": "index.js", "mappings": "AAAA", "sources": ["webpack://vue-cropper/webpack/universalModuleDefinition"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"vue-cropper\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"vue-cropper\"] = factory();\n\telse\n\t\troot[\"vue-cropper\"] = factory();\n})(self, () => {\nreturn "], "names": [], "sourceRoot": ""}