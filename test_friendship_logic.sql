-- 测试好友关系逻辑的SQL脚本

-- 假设用户A(id=1)向用户B(id=2)发送好友请求
-- 1. 创建好友请求
INSERT INTO friendships (user_id, friend_id, status, remark, deleted) 
VALUES (1, 2, 0, '来自用户A的好友请求', 0);

-- 2. 查看当前状态
SELECT 'Step 1: 发送请求后' as step;
SELECT * FROM friendships WHERE (user_id = 1 AND friend_id = 2) OR (user_id = 2 AND friend_id = 1);

-- 3. 用户B接受请求 - 更新原记录
UPDATE friendships SET status = 1 WHERE user_id = 1 AND friend_id = 2 AND status = 0;

-- 4. 创建反向记录
INSERT INTO friendships (user_id, friend_id, status, remark, deleted) 
VALUES (2, 1, 1, '', 0);

-- 5. 查看接受请求后的状态
SELECT 'Step 2: 接受请求后' as step;
SELECT * FROM friendships WHERE (user_id = 1 AND friend_id = 2) OR (user_id = 2 AND friend_id = 1);

-- 6. 测试好友列表查询
SELECT 'Step 3: 用户A的好友列表' as step;
SELECT u.id, u.username, u.nickname, f.remark, f.created_time as friend_since 
FROM friendships f 
JOIN users u ON f.friend_id = u.id 
WHERE f.user_id = 1 AND f.status = 1 AND f.deleted = 0 AND u.deleted = 0;

SELECT 'Step 4: 用户B的好友列表' as step;
SELECT u.id, u.username, u.nickname, f.remark, f.created_time as friend_since 
FROM friendships f 
JOIN users u ON f.friend_id = u.id 
WHERE f.user_id = 2 AND f.status = 1 AND f.deleted = 0 AND u.deleted = 0;

-- 清理测试数据
DELETE FROM friendships WHERE (user_id = 1 AND friend_id = 2) OR (user_id = 2 AND friend_id = 1);
