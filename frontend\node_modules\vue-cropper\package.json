{"name": "vue-cropper", "version": "0.5.11", "description": "A simple Vue picture clipping plugin", "keywords": ["vue", "cropper", "vue-cropper", "vue-component", "vue-cropper-component"], "main": "./dist/index.js", "repository": {"type": "git", "url": "git+https://github.com/xyxiao001/vue-cropper.git"}, "author": "goodboy", "license": "ISC", "bugs": {"url": "https://github.com/xyxiao001/vue-cropper/issues"}, "homepage": "https://github.com/xyxiao001/vue-cropper#readme", "scripts": {"build": "rm -rf ./dist && webpack --config webpack.config.js"}, "devDependencies": {"@babel/core": "^7.1.2", "@babel/plugin-transform-runtime": "^7.1.0", "@babel/preset-env": "^7.1.0", "babel-loader": "^8.0.0-beta.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-polyfill": "^6.26.0", "babel-runtime": "^6.26.0", "css-loader": "^1.0.0", "style-loader": "^0.23.1", "vue": "^2.5.17", "vue-loader": "^15.4.2", "vue-template-compiler": "^2.5.17", "webpack": "^5.76.0", "webpack-cli": "^5.0.1", "sass": "^1.37.5"}, "browserify": {"transform": ["vueify"]}}