# 好友申请通知功能测试文档

## 问题描述
用户发送好友申请后，对方收不到通知，需要手动刷新页面才能看到新的好友请求。

## 解决方案
实现了以下功能来解决好友申请收不到的问题：

### 1. 前端实时通知系统
- **通知服务** (`frontend/src/services/NotificationService.js`)
  - 轮询机制：每30秒检查一次新的好友请求
  - 事件系统：支持监听好友请求更新事件
  - 桌面通知：支持浏览器原生桌面通知
  - 页面通知：使用Element UI的通知组件

- **导航栏通知提醒** (`frontend/src/components/Layout/MainLayout.vue`)
  - 好友请求数量徽章显示
  - 点击跳转到好友请求页面
  - 实时更新通知数量
  - 新请求时显示页面和桌面通知

### 2. 后端通知服务
- **通知服务接口** (`backend/src/main/java/com/jingzhenjili/service/NotificationService.java`)
- **通知服务实现** (`backend/src/main/java/com/jingzhenjili/service/impl/NotificationServiceImpl.java`)
- **集成到好友服务** (`backend/src/main/java/com/jingzhenjili/service/impl/FriendshipServiceImpl.java`)
  - 发送好友请求时触发通知
  - 接受好友请求时触发通知

### 3. 用户体验优化
- **URL参数支持**：支持通过 `/friends?tab=requests` 直接跳转到好友请求页面
- **刷新功能**：添加手动刷新好友请求列表的按钮
- **视觉反馈**：通知徽章带有脉冲动画效果
- **错误处理**：网络错误时静默处理，不影响用户体验

## 测试步骤

### 测试环境准备
1. 启动后端服务
2. 启动前端服务
3. 准备两个测试账号（用户A和用户B）

### 功能测试

#### 1. 基础好友申请功能测试
```bash
# 1. 用户A登录系统
# 2. 搜索用户B
# 3. 发送好友请求
# 4. 检查是否显示"已发送"状态
```

#### 2. 实时通知功能测试
```bash
# 1. 用户B登录系统
# 2. 观察导航栏是否显示好友请求数量徽章
# 3. 检查是否收到页面通知
# 4. 检查是否收到桌面通知（需要授权）
```

#### 3. 好友请求处理测试
```bash
# 1. 用户B点击导航栏通知徽章
# 2. 验证是否正确跳转到好友请求页面
# 3. 接受或拒绝好友请求
# 4. 检查用户A是否收到处理结果通知
```

#### 4. 轮询机制测试
```bash
# 1. 用户B保持页面打开但不在好友页面
# 2. 用户A发送好友请求
# 3. 等待30秒观察用户B是否收到通知
# 4. 验证通知徽章数量是否正确更新
```

### API测试

#### 发送好友请求
```bash
curl -X POST "http://localhost:8080/api/friend/request" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "friendId": 2,
    "remark": "来自用户的好友请求"
  }'
```

#### 获取好友请求列表
```bash
curl -X GET "http://localhost:8080/api/friend/requests" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 接受好友请求
```bash
curl -X POST "http://localhost:8080/api/friend/accept/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 拒绝好友请求
```bash
curl -X POST "http://localhost:8080/api/friend/reject/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 预期结果

#### 成功场景
1. **发送请求**：用户A发送好友请求后，显示"已发送"状态
2. **接收通知**：用户B在30秒内收到通知（页面通知 + 桌面通知）
3. **徽章显示**：导航栏显示正确的好友请求数量
4. **页面跳转**：点击徽章正确跳转到好友请求页面
5. **处理请求**：接受/拒绝请求后，发送方收到相应通知
6. **状态更新**：所有相关状态实时更新

#### 错误处理
1. **网络错误**：轮询失败时不影响用户正常使用
2. **权限错误**：桌面通知权限被拒绝时仍显示页面通知
3. **重复请求**：防止重复发送好友请求

## 技术实现细节

### 前端通知流程
1. 页面加载时初始化通知服务
2. 通知服务每30秒调用API检查好友请求
3. 检测到新请求时触发事件
4. MainLayout组件监听事件并更新UI
5. 显示页面通知和桌面通知

### 后端通知流程
1. 用户发送好友请求
2. 保存请求到数据库
3. 调用通知服务记录通知日志
4. 返回成功响应

### 数据库变更
无需额外的数据库变更，使用现有的 `friendships` 表。

## 性能考虑
1. **轮询频率**：30秒间隔平衡了实时性和性能
2. **错误处理**：网络错误时静默处理，避免频繁重试
3. **内存管理**：组件销毁时清理定时器和事件监听器
4. **缓存策略**：避免重复的API调用

## 后续优化建议
1. **WebSocket实现**：替换轮询机制，实现真正的实时通知
2. **推送服务**：集成第三方推送服务（如极光推送）
3. **邮件通知**：重要通知通过邮件发送
4. **通知历史**：保存通知历史记录到数据库
5. **通知设置**：允许用户自定义通知偏好

## 测试结果记录

### 测试日期：[填写测试日期]
### 测试人员：[填写测试人员]

| 测试项目 | 预期结果 | 实际结果 | 状态 | 备注 |
|---------|---------|---------|------|------|
| 发送好友请求 | 显示已发送状态 | | | |
| 接收通知 | 30秒内收到通知 | | | |
| 徽章显示 | 显示正确数量 | | | |
| 页面跳转 | 正确跳转 | | | |
| 处理请求 | 状态正确更新 | | | |
| 桌面通知 | 显示桌面通知 | | | |
| 错误处理 | 优雅处理错误 | | | |

## 问题记录
[记录测试过程中发现的问题]

## 修复记录
[记录问题的修复情况]
