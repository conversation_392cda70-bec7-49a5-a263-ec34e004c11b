package com.jingzhenjili.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingzhenjili.entity.Friendship;
import com.jingzhenjili.vo.FriendVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 好友关系Mapper接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Mapper
public interface FriendshipMapper extends BaseMapper<Friendship> {

    /**
     * 获取用户的好友列表
     * 
     * @param userId 用户ID
     * @return 好友列表
     */
    @Select("SELECT u.id, u.username, u.nickname, u.avatar, u.gender, u.status, " +
            "f.remark, f.created_time as friend_since " +
            "FROM friendships f " +
            "JOIN users u ON f.friend_id = u.id " +
            "WHERE f.user_id = #{userId} AND f.status = 1 AND f.deleted = 0 AND u.deleted = 0 " +
            "ORDER BY f.created_time DESC")
    List<FriendVO> getFriendList(@Param("userId") Long userId);

    /**
     * 获取用户的好友请求列表（收到的）
     * 
     * @param userId 用户ID
     * @return 好友请求列表
     */
    @Select("SELECT u.id, u.username, u.nickname, u.avatar, u.gender, u.status, " +
            "f.remark, f.created_time as request_time, f.id as friendship_id " +
            "FROM friendships f " +
            "JOIN users u ON f.user_id = u.id " +
            "WHERE f.friend_id = #{userId} AND f.status = 0 AND f.deleted = 0 AND u.deleted = 0 " +
            "ORDER BY f.created_time DESC")
    List<FriendVO> getFriendRequests(@Param("userId") Long userId);

    /**
     * 获取用户发送的好友请求列表
     * 
     * @param userId 用户ID
     * @return 发送的好友请求列表
     */
    @Select("SELECT u.id, u.username, u.nickname, u.avatar, u.gender, u.status, " +
            "f.remark, f.created_time as request_time, f.id as friendship_id, f.status as request_status " +
            "FROM friendships f " +
            "JOIN users u ON f.friend_id = u.id " +
            "WHERE f.user_id = #{userId} AND f.status IN (0, 2) AND f.deleted = 0 AND u.deleted = 0 " +
            "ORDER BY f.created_time DESC")
    List<FriendVO> getSentFriendRequests(@Param("userId") Long userId);

    /**
     * 检查好友关系是否存在
     * 
     * @param userId 用户ID
     * @param friendId 好友ID
     * @return 好友关系
     */
    @Select("SELECT * FROM friendships WHERE user_id = #{userId} AND friend_id = #{friendId} AND deleted = 0")
    Friendship checkFriendship(@Param("userId") Long userId, @Param("friendId") Long friendId);

    /**
     * 查询任意状态（包含软删除）的好友关系
     * 注意：此查询不会附加逻辑删除条件，用于避免唯一索引冲突时做“复活”处理
     *
     * @param userId 用户ID
     * @param friendId 好友ID
     * @return 好友关系（可能deleted=1）
     */
    @Select("SELECT * FROM friendships WHERE user_id = #{userId} AND friend_id = #{friendId} LIMIT 1")
    Friendship findAnyFriendship(@Param("userId") Long userId, @Param("friendId") Long friendId);

    /**
     * 检查是否为好友关系（双向确认）
     * 
     * @param userId 用户ID
     * @param friendId 好友ID
     * @return 是否为好友
     */
    @Select("SELECT COUNT(*) > 0 FROM friendships " +
            "WHERE ((user_id = #{userId} AND friend_id = #{friendId}) OR (user_id = #{friendId} AND friend_id = #{userId})) " +
            "AND status = 1 AND deleted = 0")
    Boolean isFriend(@Param("userId") Long userId, @Param("friendId") Long friendId);

    /**
     * 获取用户好友数量
     * 
     * @param userId 用户ID
     * @return 好友数量
     */
    @Select("SELECT COUNT(*) FROM friendships WHERE user_id = #{userId} AND status = 1 AND deleted = 0")
    Integer getFriendCount(@Param("userId") Long userId);

    /**
     * 获取共同好友列表
     * 
     * @param userId1 用户1ID
     * @param userId2 用户2ID
     * @return 共同好友列表
     */
    @Select("SELECT u.id, u.username, u.nickname, u.avatar, u.gender " +
            "FROM users u " +
            "WHERE u.id IN (" +
            "  SELECT f1.friend_id FROM friendships f1 " +
            "  WHERE f1.user_id = #{userId1} AND f1.status = 1 AND f1.deleted = 0" +
            ") AND u.id IN (" +
            "  SELECT f2.friend_id FROM friendships f2 " +
            "  WHERE f2.user_id = #{userId2} AND f2.status = 1 AND f2.deleted = 0" +
            ") AND u.deleted = 0")
    List<FriendVO> getCommonFriends(@Param("userId1") Long userId1, @Param("userId2") Long userId2);
}
