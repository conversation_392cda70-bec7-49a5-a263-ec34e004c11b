-- 添加个人简介字段
-- 首先检查字段是否存在，如果不存在则添加
SET @dbname = 'competition_incentive';
SET @tablename = 'users';
SET @columnname = 'bio';
SET @columntype = 'TEXT';
SET @columncomment = '个人简介';

-- 检查字段是否存在
SET @query = CONCAT('SELECT COUNT(*) INTO @exists FROM information_schema.columns WHERE table_schema = ''', @dbname, ''' AND table_name = ''', @tablename, ''' AND column_name = ''', @columnname, '''');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 如果字段不存在，则添加
SET @query = CONCAT('SELECT IF(@exists = 0, CONCAT(''ALTER TABLE '', ''', @tablename, ''' ,'' ADD COLUMN '', ''', @columnname, ''' '', ''', @columntype, ''' COMMENT ''', @columncomment, ''';''), ''SELECT "字段已存在，无需添加";'') INTO @sql');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 执行添加字段的SQL
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;