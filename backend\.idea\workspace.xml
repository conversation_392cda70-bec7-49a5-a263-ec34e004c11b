<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="4891c720-b384-4ea6-bdd7-ad193c9e452d" name="变更" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="F:\environment\maven\apache-maven-3.6.3\maven-repo" />
        <option name="userSettingsFile" value="F:\environment\maven\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2zB1GTvqd2IWWdClhVwDQOJYgnb" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.CompetitionIncentiveApplication.executor&quot;: &quot;Run&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/projects/vue/love/backend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="CompetitionIncentiveApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="competition-incentive-backend" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.jingzhenjili.CompetitionIncentiveApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="jdk-1.8.0_402-corretto-1.8.0_402-4caba194b151-ae57be32" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="4891c720-b384-4ea6-bdd7-ad193c9e452d" name="变更" comment="" />
      <created>1751189561471</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751189561471</updated>
      <workItem from="1751189562824" duration="98000" />
      <workItem from="1751190017759" duration="4008000" />
      <workItem from="1751375905946" duration="70000" />
      <workItem from="1752973212318" duration="3579000" />
      <workItem from="1753171005596" duration="1190000" />
      <workItem from="1753261922048" duration="2393000" />
      <workItem from="1753323688668" duration="29000" />
      <workItem from="1754122551936" duration="3787000" />
      <workItem from="1754701689729" duration="6459000" />
      <workItem from="1754802707288" duration="8330000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>