<template>
  <div class="user-profile">
    <div class="profile-banner">
      <div class="banner-overlay"></div>
    </div>

    <div class="profile-header">
      <div class="avatar-section">
        <div class="avatar-container" @mouseenter="showAvatarOverlay = true" @mouseleave="showAvatarOverlay = false">
          <el-avatar :src="userInfo.avatar" :size="120" class="user-avatar">
          {{ userInfo.nickname ? userInfo.nickname.charAt(0) : 'U' }}
        </el-avatar>
          <div class="avatar-overlay" v-show="showAvatarOverlay" @click="triggerFileInput">
            <i class="el-icon-camera"></i>
            <span>更换头像</span>
          </div>
        </div>
        <input
          type="file"
          ref="avatarInput"
          accept="image/jpeg,image/png,image/gif"
          style="display:none"
          @change="handleAvatarChange"
        />
    <!-- 裁剪弹窗 -->
    <el-dialog
      title="裁剪头像"
      :visible.sync="cropperVisible"
      width="820px"
      :append-to-body="true"
      modal-class="cropper-modal"
      custom-class="avatar-cropper-dialog"
      :destroy-on-close="true"
      @close="onCropperClose"
    >
      <div class="cropper-dialog-body">
        <div class="cropper-left">
          <vue-cropper
            ref="cropper"
            :img="cropperImg"
            :outputType="'png'"
            :autoCrop="true"
            :autoCropWidth="300"
            :autoCropHeight="300"
            :fixed="true"
            :fixedNumber="[1,1]"
            :canMove="true"
            :canMoveBox="true"
            :centerBox="true"
            :maxImgSize="3000"
            :full="true"
            @realTime="onCropperRealTime"
            :style="{ width: '100%', height: '460px' }"
          />
        </div>
        <div class="cropper-right">
          <div class="preview-title">预览</div>
          <div class="preview-box">
            <div class="preview" v-if="previews.url" :style="previews.div">
              <img :src="previews.url" :style="previews.img" />
            </div>
            <div class="preview placeholder" v-else></div>
          </div>
          <div class="tips">拖拽或缩放以调整裁剪区域，建议使用正方形头像。</div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cropperVisible = false">取 消</el-button>
        <el-button type="primary" :loading="avatarLoading" @click="confirmCropUpload">确 定</el-button>
      </span>
    </el-dialog>
      </div>
      
      <div class="info-section">
        <div class="user-info-header">
          <div class="user-name-area">
        <h1 class="username">{{ userInfo.nickname || userInfo.username }}</h1>
            <span class="user-badge" v-if="userInfo.isVip">
              <i class="el-icon-star-on"></i> VIP会员
            </span>
          </div>
          <el-button type="primary" size="small" icon="el-icon-edit" @click="activeTab = 'info'">编辑资料</el-button>
        </div>
        <p class="user-email">
          <i class="el-icon-message"></i>
          {{ userInfo.email }}
        </p>
        <p class="user-bio" v-if="userInfo.bio">{{ userInfo.bio }}</p>

        <div class="user-stats">
          <div class="stat-item">
            <span class="stat-value">{{ userInfo.totalPoints || 0 }}</span>
            <span class="stat-label">总积分</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ userInfo.completedTasks || 0 }}</span>
            <span class="stat-label">完成任务</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ userInfo.winCount || 0 }}</span>
            <span class="stat-label">竞争胜利</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ userInfo.friendCount || 0 }}</span>
            <span class="stat-label">好友数量</span>
          </div>
        </div>
      </div>
    </div>

    <div class="profile-content">
      <el-tabs v-model="activeTab" class="custom-tabs">
        <el-tab-pane label="个人信息" name="info">
          <div class="info-form">
            <div class="form-header">
              <h2>编辑个人资料</h2>
              <p>完善你的个人信息，让其他用户更好地了解你</p>
            </div>
            <el-form
              ref="profileForm"
              :model="profileForm"
              :rules="profileRules"
              label-width="100px"
              class="profile-form"
            >
              <el-form-item label="昵称" prop="nickname">
                <el-input
                  v-model="profileForm.nickname"
                  placeholder="请输入昵称"
                  prefix-icon="el-icon-user"
                />
              </el-form-item>
              
              <el-form-item label="性别" prop="gender">
                <el-radio-group v-model="profileForm.gender" class="gender-group">
                  <el-radio :label="0">
                    <i class="el-icon-question"></i> 保密
                  </el-radio>
                  <el-radio :label="1">
                    <i class="el-icon-male"></i> 男
                  </el-radio>
                  <el-radio :label="2">
                    <i class="el-icon-female"></i> 女
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="手机号" prop="phone">
                <el-input
                  v-model="profileForm.phone"
                  placeholder="请输入手机号"
                  prefix-icon="el-icon-mobile-phone"
                />
              </el-form-item>
              
              <el-form-item label="个人简介">
                <el-input
                  v-model="profileForm.bio"
                  type="textarea"
                  :rows="4"
                  placeholder="介绍一下自己吧"
                  maxlength="200"
                  show-word-limit
                  class="bio-textarea"
                />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" :loading="loading" @click="updateProfile" class="save-btn">
                  <i class="el-icon-check"></i>
                  {{ loading ? '保存中...' : '保存修改' }}
                </el-button>
                <el-button @click="resetForm" plain>
                  <i class="el-icon-refresh"></i>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="成就记录" name="achievements">
          <div class="achievements-section">
            <div class="section-header">
              <h2>我的成就</h2>
              <p>记录你在平台上获得的所有成就</p>
            </div>

            <div class="achievement-stats">
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="el-icon-trophy"></i>
                </div>
                <div class="stat-content">
                  <span class="stat-number">{{ achievements.filter(a => a.unlocked).length }}</span>
                  <span class="stat-text">已解锁</span>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="el-icon-lock"></i>
                </div>
                <div class="stat-content">
                  <span class="stat-number">{{ achievements.filter(a => !a.unlocked).length }}</span>
                  <span class="stat-text">待解锁</span>
                </div>
              </div>
            </div>

            <div class="achievement-grid">
              <div
                v-for="achievement in achievements"
                :key="achievement.id"
                class="achievement-card"
                :class="{ unlocked: achievement.unlocked }"
              >
                <div class="achievement-icon">
                  <i :class="achievement.icon"></i>
                </div>
                <div class="achievement-info">
                  <div class="achievement-header">
                  <h4>{{ achievement.title }}</h4>
                    <span class="achievement-status" :class="{ unlocked: achievement.unlocked }">
                      {{ achievement.unlocked ? '已解锁' : '未解锁' }}
                  </span>
                  </div>
                  <p>{{ achievement.description }}</p>
                  <div v-if="achievement.unlocked" class="unlock-info">
                    <i class="el-icon-time"></i>
                    <span class="unlock-time">{{ formatDate(achievement.unlockTime) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="积分记录" name="points">
          <div class="points-history">
            <div class="section-header">
              <h2>积分记录</h2>
              <p>你的积分变动历史</p>
            </div>

            <div class="points-summary">
              <div class="summary-card">
                <div class="summary-icon">
                  <i class="el-icon-medal"></i>
                </div>
                <div class="summary-content">
                  <span class="summary-number">{{ userInfo.totalPoints || 0 }}</span>
                  <span class="summary-text">当前积分</span>
                </div>
              </div>
              <div class="summary-card">
                <div class="summary-icon positive">
                  <i class="el-icon-top"></i>
                </div>
                <div class="summary-content">
                  <span class="summary-number">
                    {{ pointsHistory.filter(r => r.points > 0).reduce((sum, r) => sum + r.points, 0) }}
                  </span>
                  <span class="summary-text">累计获得</span>
                </div>
              </div>
              <div class="summary-card">
                <div class="summary-icon negative">
                  <i class="el-icon-bottom"></i>
                </div>
                <div class="summary-content">
                  <span class="summary-number">
                    {{ Math.abs(pointsHistory.filter(r => r.points < 0).reduce((sum, r) => sum + r.points, 0)) }}
                  </span>
                  <span class="summary-text">累计消耗</span>
                </div>
              </div>
            </div>

            <div class="points-list">
            <div
              v-for="record in pointsHistory"
              :key="record.id"
              class="points-record"
            >
              <div class="record-info">
                  <div class="record-icon" :class="{ positive: record.points > 0, negative: record.points < 0 }">
                    <i :class="record.points > 0 ? 'el-icon-plus' : 'el-icon-minus'"></i>
                  </div>
                  <div class="record-content">
                <h4>{{ record.description }}</h4>
                <span class="record-time">{{ formatDate(record.createdTime) }}</span>
                  </div>
              </div>
              <div class="record-points" :class="{ positive: record.points > 0, negative: record.points < 0 }">
                {{ record.points > 0 ? '+' : '' }}{{ record.points }}
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { VueCropper } from 'vue-cropper'

export default {
  name: 'UserProfile',
  components: { VueCropper },
  
  data() {
    return {
      loading: false,
      avatarLoading: false,
      cropperVisible: false,
      cropperImg: '',
      rawAvatarFile: null,
      loadingInstance: null,
      previews: { url: '', img: {}, div: {} },
      showAvatarOverlay: false,
      activeTab: 'info',
      userInfo: {},
      profileForm: {
        nickname: '',
        gender: 0,
        phone: '',
        bio: ''
      },
      profileRules: {
        nickname: [
          { min: 2, max: 20, message: '昵称长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ]
      },
      achievements: [],
      pointsHistory: []
    }
  },
  
  created() {
    this.loadUserInfo()
    this.loadAchievements()
    this.loadPointsHistory()
  },
  
  methods: {
    async loadUserInfo() {
      try {
        const response = await this.$api.user.getUserInfo()

        // 添加调试日志
        console.log('获取到的用户信息:', response.data)

        this.userInfo = response.data
        
        // 初始化表单
        this.profileForm = {
          nickname: this.userInfo.nickname || '',
          gender: this.userInfo.gender || 0,
          phone: this.userInfo.phone || '',
          bio: this.userInfo.bio || ''
        }

        // 添加调试日志
        console.log('初始化的表单数据:', this.profileForm)
      } catch (error) {
        this.$message.error('加载用户信息失败')
        console.error('加载用户信息失败详情:', error)
      }
    },
    
    async loadAchievements() {
      try {
        // 模拟成就数据
        this.achievements = [
          {
            id: 1,
            title: '初来乍到',
            description: '完成第一个任务',
            icon: 'el-icon-trophy',
            unlocked: true,
            unlockTime: new Date()
          },
          {
            id: 2,
            title: '竞争达人',
            description: '参与10次竞争',
            icon: 'el-icon-medal',
            unlocked: false
          }
        ]
      } catch (error) {
        console.error('加载成就失败:', error)
      }
    },
    
    async loadPointsHistory() {
      try {
        // 模拟积分记录
        this.pointsHistory = [
          {
            id: 1,
            description: '完成任务：每日阅读',
            points: 10,
            createdTime: new Date()
          },
          {
            id: 2,
            description: '竞争胜利奖励',
            points: 20,
            createdTime: new Date()
          }
        ]
      } catch (error) {
        console.error('加载积分记录失败:', error)
      }
    },
    
    async updateProfile() {
      try {
        const valid = await this.$refs.profileForm.validate()
        if (!valid) return
        
        this.loading = true
        
        // 添加调试日志
        console.log('提交的表单数据:', this.profileForm)

        const response = await this.$api.user.updateUserInfo(this.profileForm)

        // 添加调试日志
        console.log('更新成功，服务器响应:', response)
        
        this.$message.success('个人信息更新成功')
        this.loadUserInfo()
        
      } catch (error) {
        this.$message.error('更新失败: ' + (error.response?.data?.message || error.message || '未知错误'))
        console.error('更新失败详情:', error)
      } finally {
        this.loading = false
      }
    },
    
    triggerFileInput() {
      this.$refs.avatarInput.click()
    },

    async handleAvatarChange(e) {
      const file = e.target.files[0]
      if (!file) return

      // 验证文件类型
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif']
      if (!allowedTypes.includes(file.type)) {
        this.$message.error('只能上传JPG、PNG或GIF格式的图片')
        return
      }

      // 验证文件大小（最大5MB）
      const maxSize = 5 * 1024 * 1024
      if (file.size > maxSize) {
        this.$message.error('图片大小不能超过5MB')
        return
      }

      // 进入裁剪流程
      this.rawAvatarFile = file
      this.cropperImg = URL.createObjectURL(file)
      this.cropperVisible = true
      // 清空文件输入框，以便再次选择同一文件时也能触发change事件
      this.$refs.avatarInput.value = ''
    },
    onCropperClose() {
      if (this.cropperImg) {
        URL.revokeObjectURL(this.cropperImg)
      }
      this.cropperImg = ''
      this.rawAvatarFile = null
      this.previews = { url: '', img: {}, div: {} } // 关闭时清空预览
    },
    onCropperRealTime(data) {
      this.previews = data
    },
    async confirmCropUpload() {
      if (!this.$refs.cropper) return
      try {
        this.avatarLoading = true
        this.loadingInstance = this.$loading({
          lock: true,
          text: '头像上传中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        // 获取裁剪后的Blob并上传
        this.$refs.cropper.getCropBlob(async (blob) => {
          try {
            const croppedFile = new File([blob], 'avatar.png', { type: 'image/png' })
            const response = await this.$api.user.uploadAvatar(croppedFile)
            this.userInfo.avatar = response.data.url
            this.$message.success('头像上传成功')
            this.cropperVisible = false
          } catch (err) {
            this.$message.error('头像上传失败：' + (err.message || '未知错误'))
            console.error('头像上传失败:', err)
          } finally {
            this.avatarLoading = false
            if (this.loadingInstance) this.loadingInstance.close()
            this.onCropperClose()
          }
        })
      } catch (error) {
        this.avatarLoading = false
        if (this.loadingInstance) this.loadingInstance.close()
        this.$message.error('裁剪失败：' + (error.message || '未知错误'))
      }
    },

    resetForm() {
      // 重置表单为用户当前信息
      this.profileForm = {
        nickname: this.userInfo.nickname || '',
        gender: this.userInfo.gender || 0,
        phone: this.userInfo.phone || '',
        bio: this.userInfo.bio || ''
      }

      // 重置表单验证
      if (this.$refs.profileForm) {
        this.$refs.profileForm.clearValidate()
      }

      this.$message.info('表单已重置')
    },
    
    formatDate(date) {
      return this.$utils.formatDate(date, 'YYYY-MM-DD HH:mm')
    }
  }
}
</script>

<style lang="scss" scoped>
.user-profile {
  padding: 0;
  max-width: 1000px;
  margin: 0 auto;
  position: relative;

  .profile-banner {
    height: 200px;
    background: linear-gradient(135deg, #5a67d8, #805ad5);
    border-radius: 12px 12px 0 0;
    position: relative;
    overflow: hidden;
    z-index: 1;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('https://cdn.pixabay.com/photo/2018/01/14/23/12/nature-3082832_1280.jpg') center/cover;
      opacity: 0.3;
      z-index: 0;
    }

    .banner-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.4));
      z-index: 1;
      pointer-events: none;
    }
  }
  
  .profile-header {
    background: white;
    padding: 30px 40px 40px;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    gap: 40px;
    margin-top: -80px;
    position: relative;
    z-index: 2;
    
    .avatar-section {
      text-align: center;
      
      .avatar-container {
        position: relative;
        display: inline-block;
        border-radius: 50%;
        overflow: hidden;
        margin-bottom: 12px;
        cursor: pointer;

        .user-avatar {
          border: 4px solid rgba(255, 255, 255, 0.8);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
        }

        .avatar-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.6);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: white;
          opacity: 0;
          transition: all 0.3s ease;

          &:hover {
            opacity: 1;
          }

          i {
            font-size: 32px;
            margin-bottom: 8px;
          }

          span {
            font-size: 14px;
            font-weight: 500;
          }
        }

        &:hover {
          .avatar-overlay {
            opacity: 1;
          }

          .user-avatar {
            transform: scale(1.02);
          }
        }
      }
    }
    
    .info-section {
      flex: 1;

      .user-info-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .user-name-area {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .user-badge {
          background: linear-gradient(90deg, #f6ad55, #ed8936);
          color: white;
          padding: 4px 10px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 4px;

          i {
            font-size: 14px;
          }
        }
      }
      
      .username {
        font-size: 28px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
      }
      
      .user-email {
        font-size: 16px;
        color: #7f8c8d;
        margin: 8px 0;
        display: flex;
        align-items: center;
        gap: 6px;

        i {
          color: #5a67d8;
        }
      }

      .user-bio {
        font-size: 15px;
        color: #4a5568;
        margin: 12px 0 20px;
        line-height: 1.5;
        padding: 12px;
        background: #f8fafc;
        border-radius: 8px;
        border-left: 3px solid #5a67d8;
        white-space: pre-wrap;
        word-break: break-word;
      }
      
      .user-stats {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-top: 24px;
        background: #f8fafc;
        padding: 16px;
        border-radius: 12px;
        
        .stat-item {
          text-align: center;
          flex: 1;
          min-width: 100px;
          padding: 12px;
          background: white;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
          
          .stat-value {
            display: block;
            font-size: 24px;
            font-weight: 600;
            background: linear-gradient(90deg, #5a67d8, #805ad5);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #718096;
            font-weight: 500;
          }
        }
      }
    }
  }
  
  .profile-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    padding: 0;

    .custom-tabs {
      ::v-deep .el-tabs__header {
        margin-bottom: 0;
        background: #f8fafc;
        padding: 0 20px;

        .el-tabs__nav-wrap::after {
          height: 1px;
          background: rgba(0, 0, 0, 0.05);
        }

        .el-tabs__item {
          height: 56px;
          line-height: 56px;
          font-size: 16px;
          font-weight: 500;
          color: #718096;
          transition: all 0.3s ease;

          &.is-active {
            color: #5a67d8;
            font-weight: 600;
          }

          &:hover {
            color: #5a67d8;
          }
        }

        .el-tabs__active-bar {
          background: #5a67d8;
          height: 3px;
        }
      }

      ::v-deep .el-tabs__content {
        padding: 0;
      }
    }

    .section-header {
      padding: 24px 30px;
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 20px;

      h2 {
        font-size: 20px;
        font-weight: 600;
        color: #2d3748;
        margin: 0 0 8px 0;
      }

      p {
        font-size: 14px;
        color: #718096;
        margin: 0;
      }
    }
    
    .info-form {
      padding: 0 30px 30px;

      .form-header {
        padding: 24px 0;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 30px;

        h2 {
          font-size: 20px;
          font-weight: 600;
          color: #2d3748;
          margin: 0 0 8px 0;
        }

        p {
          font-size: 14px;
          color: #718096;
          margin: 0;
        }
      }

      .profile-form {
        max-width: 600px;

        ::v-deep .el-form-item__label {
          font-weight: 500;
          color: #4a5568;
        }

        ::v-deep .el-input__inner {
          border-radius: 8px;
          border: 1px solid #e2e8f0;
          padding: 12px 15px;
          height: 44px;
          transition: all 0.3s ease;

          &:focus {
            border-color: #5a67d8;
            box-shadow: 0 0 0 3px rgba(90, 103, 216, 0.15);
          }
        }

        ::v-deep .el-input__prefix {
          left: 12px;
          color: #a0aec0;
        }

        ::v-deep .el-input--prefix .el-input__inner {
          padding-left: 40px;
        }

        .gender-group {
          ::v-deep .el-radio {
            margin-right: 24px;

            .el-radio__label {
              display: flex;
              align-items: center;
              gap: 4px;
              padding-left: 8px;
            }

            .el-radio__input.is-checked + .el-radio__label {
              color: #5a67d8;
            }

            .el-radio__inner {
              border-color: #cbd5e0;

              &::after {
                background-color: #5a67d8;
              }
            }

            .el-radio__input.is-checked .el-radio__inner {
              border-color: #5a67d8;
              background: white;
            }
          }
        }

        .bio-textarea {
          ::v-deep .el-textarea__inner {
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            padding: 12px 15px;
            transition: all 0.3s ease;

            &:focus {
              border-color: #5a67d8;
              box-shadow: 0 0 0 3px rgba(90, 103, 216, 0.15);
            }
          }
        }

        .save-btn {
          padding: 12px 24px;
          border-radius: 8px;
          background: linear-gradient(90deg, #5a67d8, #805ad5);
          border: none;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(90, 103, 216, 0.3);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
    
    .achievements-section {
      padding: 0 0 30px;

      .achievement-stats {
        display: flex;
        gap: 20px;
        padding: 0 30px 20px;

        .stat-card {
          flex: 1;
          background: #f8fafc;
          border-radius: 12px;
          padding: 20px;
          display: flex;
          align-items: center;
          gap: 16px;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
          }

          .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #5a67d8, #805ad5);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
          }

          .stat-content {
            .stat-number {
              display: block;
              font-size: 28px;
              font-weight: 700;
              color: #2d3748;
              margin-bottom: 4px;
            }

            .stat-text {
              font-size: 14px;
              color: #718096;
              font-weight: 500;
            }
          }
        }
      }
      
      .achievement-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        padding: 0 30px;
        
        .achievement-card {
          padding: 20px;
          border: 2px solid #ebeef5;
          border-radius: 12px;
          display: flex;
          align-items: flex-start;
          gap: 16px;
          opacity: 0.7;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #cbd5e0;
          opacity: 0.5;
          }
          
          &.unlocked {
            opacity: 1;
            border-color: #5a67d8;
            background: #f8fafc;

            &::before {
              background: linear-gradient(90deg, #5a67d8, #805ad5);
              opacity: 1;
            }

            &:hover {
              transform: translateY(-3px);
              box-shadow: 0 8px 16px rgba(90, 103, 216, 0.15);
            }
          }
          
          .achievement-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #f7fafc;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #a0aec0;

            .unlocked & {
              background: linear-gradient(135deg, #5a67d8, #805ad5);
              color: white;
              box-shadow: 0 4px 12px rgba(90, 103, 216, 0.2);
            }
          }
          
          .achievement-info {
            flex: 1;
            
            .achievement-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;

            h4 {
              font-size: 16px;
              font-weight: 600;
                color: #2d3748;
                margin: 0;
              }

              .achievement-status {
                font-size: 12px;
                font-weight: 500;
                padding: 2px 8px;
                border-radius: 12px;
                background: #edf2f7;
                color: #718096;

                &.unlocked {
                  background: #ebf4ff;
                  color: #5a67d8;
                }
              }
            }
            
            p {
              font-size: 14px;
              color: #718096;
              margin: 0 0 8px 0;
              line-height: 1.5;
            }
            
            .unlock-info {
              display: flex;
              align-items: center;
              gap: 6px;
              font-size: 12px;
              color: #5a67d8;

              i {
                font-size: 14px;
              }

              .unlock-time {
                color: #718096;
              }
            }
          }
        }
      }
    }
    
    .points-history {
      padding: 0 0 30px;

      .points-summary {
        display: flex;
        gap: 20px;
        padding: 0 30px 20px;

        .summary-card {
          flex: 1;
          background: #f8fafc;
          border-radius: 12px;
          padding: 20px;
          display: flex;
          align-items: center;
          gap: 16px;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
          }

          .summary-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #5a67d8, #805ad5);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;

            &.positive {
              background: linear-gradient(135deg, #48bb78, #38a169);
            }

            &.negative {
              background: linear-gradient(135deg, #f56565, #e53e3e);
            }
          }

          .summary-content {
            .summary-number {
              display: block;
              font-size: 28px;
              font-weight: 700;
              color: #2d3748;
              margin-bottom: 4px;
            }

            .summary-text {
              font-size: 14px;
              color: #718096;
              font-weight: 500;
            }
          }
        }
      }

      .points-list {
        padding: 0 30px;
      
      .points-record {
        display: flex;
        justify-content: space-between;
        align-items: center;
          padding: 16px;
          border-bottom: 1px solid #edf2f7;
          transition: all 0.3s ease;

          &:hover {
            background: #f8fafc;
          }
        
        &:last-child {
          border-bottom: none;
        }
        
        .record-info {
            display: flex;
            align-items: center;
            gap: 16px;

            .record-icon {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              background: #edf2f7;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 18px;
              color: #718096;

              &.positive {
                background: #f0fff4;
                color: #48bb78;
              }

              &.negative {
                background: #fff5f5;
                color: #f56565;
              }
            }

            .record-content {
              h4 {
                font-size: 15px;
            font-weight: 500;
                color: #2d3748;
            margin: 0 0 4px 0;
          }
          
          .record-time {
            font-size: 12px;
                color: #a0aec0;
              }
          }
        }
        
        .record-points {
            font-size: 18px;
          font-weight: 600;
            border-radius: 8px;
            padding: 4px 12px;
          
          &.positive {
              color: #48bb78;
              background: #f0fff4;
          }
          
          &.negative {
              color: #f56565;
              background: #fff5f5;
            }
          }
        }
      }
    }
  }
}

/* 自定义弹窗与遮罩 */
:deep(.cropper-modal) {
  background: rgba(0, 0, 0, 0.25) !important;
}
:deep(.avatar-cropper-dialog .el-dialog__body) {
  background: #ffffff;
}

/* 裁剪弹窗样式 */
.cropper-dialog-body {
  display: flex;
  gap: 20px;
  background: #ffffff;
  padding-top: 4px;
}
.cropper-left {
  flex: 1 1 460px;
  min-height: 420px;
  background: #ffffff;
}
.cropper-right {
  width: 260px;
}
.preview-title {
  font-size: 14px;
  color: #4a5568;
  margin-bottom: 8px;
}
.preview-box {
  background: #f7fafc;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.preview {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  overflow: hidden;
}
.preview.placeholder {
  background: #edf2f7;
}
.tips {
  font-size: 12px;
  color: #718096;
  margin-top: 12px;
}

// 响应式设计
@media (max-width: 1024px) {
  .user-profile {
    .achievement-stats,
    .points-summary {
      flex-direction: column;

      .stat-card,
      .summary-card {
        flex-direction: row;
      }
    }
  }
}

@media (max-width: 768px) {
  .user-profile {
    padding: 0 15px;

    .profile-banner {
      border-radius: 8px 8px 0 0;
    }
    
    .profile-header {
      flex-direction: column;
      text-align: center;
      padding: 20px;
      gap: 20px;

      .user-info-header {
        flex-direction: column;
        gap: 12px;
      }
      
      .user-stats {
        justify-content: center;
        gap: 12px;
        padding: 12px;

        .stat-item {
          padding: 8px;
          min-width: 80px;
        }
      }
    }

    .profile-content {
      border-radius: 8px;

      .custom-tabs {
        ::v-deep .el-tabs__item {
          padding: 0 10px;
          font-size: 14px;
        }
      }

      .section-header,
      .form-header {
        padding: 16px;
      }

      .info-form {
        padding: 0 16px 16px;
      }

      .achievement-grid {
        grid-template-columns: 1fr;
        padding: 0 16px;
      }

      .achievement-stats,
      .points-summary {
        padding: 0 16px 16px;
      }

      .points-list {
        padding: 0 16px;
      }
    }
  }
}

@media (max-width: 480px) {
  .user-profile {
    .profile-header {
      margin-top: -60px;

      .avatar-section {
        .avatar-container {
          .user-avatar {
            width: 100px !important;
            height: 100px !important;
            font-size: 40px;
          }
        }
      }

      .username {
        font-size: 22px;
      }

      .user-stats {
        flex-wrap: wrap;

        .stat-item {
          flex: 0 0 calc(50% - 8px);

          .stat-value {
            font-size: 20px;
          }
        }
      }
    }

    .profile-content {
      .custom-tabs {
        ::v-deep .el-tabs__item {
          height: 48px;
          line-height: 48px;
          font-size: 13px;
          padding: 0 8px;
        }
      }

      .achievement-stats,
      .points-summary {
        .stat-card,
        .summary-card {
          .stat-icon,
          .summary-icon {
            width: 50px;
            height: 50px;
            font-size: 22px;
          }

          .stat-number,
          .summary-number {
            font-size: 22px;
          }
        }
      }

      .gender-group {
        ::v-deep .el-radio {
          margin-right: 12px;
          margin-bottom: 8px;
        }
      }
    }
  }
}
</style>
