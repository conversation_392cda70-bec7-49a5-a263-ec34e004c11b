/**
 * 通知服务
 * 处理好友请求、竞争邀请等实时通知
 */
class NotificationService {
  constructor() {
    this.listeners = new Map()
    this.pollingInterval = null
    this.isPolling = false
    this.api = null
  }

  /**
   * 初始化通知服务
   * @param {Object} api - API实例
   */
  init(api) {
    this.api = api
  }

  /**
   * 开始轮询通知
   * @param {number} interval - 轮询间隔（毫秒）
   */
  startPolling(interval = 30000) {
    if (this.isPolling) {
      return
    }

    this.isPolling = true
    
    // 立即检查一次
    this.checkNotifications()
    
    // 设置定时轮询
    this.pollingInterval = setInterval(() => {
      this.checkNotifications()
    }, interval)
  }

  /**
   * 停止轮询通知
   */
  stopPolling() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
      this.pollingInterval = null
    }
    this.isPolling = false
  }

  /**
   * 检查所有通知
   */
  async checkNotifications() {
    try {
      await Promise.all([
        this.checkFriendRequests(),
        this.checkCompetitionInvites()
      ])
    } catch (error) {
      console.error('检查通知失败:', error)
    }
  }

  /**
   * 检查好友请求
   */
  async checkFriendRequests() {
    if (!this.api) return

    try {
      const response = await this.api.friend.getFriendRequests()
      const requests = response.data || []
      
      this.emit('friendRequestsUpdate', {
        count: requests.length,
        requests: requests
      })
    } catch (error) {
      console.error('检查好友请求失败:', error)
    }
  }

  /**
   * 检查竞争邀请
   */
  async checkCompetitionInvites() {
    if (!this.api) return

    try {
      // TODO: 实现竞争邀请检查
      // const response = await this.api.competition.getInvites()
      // const invites = response.data || []
      
      this.emit('competitionInvitesUpdate', {
        count: 0,
        invites: []
      })
    } catch (error) {
      console.error('检查竞争邀请失败:', error)
    }
  }

  /**
   * 添加事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  /**
   * 移除事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  off(event, callback) {
    if (!this.listeners.has(event)) {
      return
    }
    
    const callbacks = this.listeners.get(event)
    const index = callbacks.indexOf(callback)
    if (index > -1) {
      callbacks.splice(index, 1)
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    if (!this.listeners.has(event)) {
      return
    }
    
    const callbacks = this.listeners.get(event)
    callbacks.forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error('通知回调执行失败:', error)
      }
    })
  }

  /**
   * 显示桌面通知
   * @param {string} title - 通知标题
   * @param {string} body - 通知内容
   * @param {Object} options - 通知选项
   */
  showDesktopNotification(title, body, options = {}) {
    if (!('Notification' in window)) {
      console.warn('浏览器不支持桌面通知')
      return
    }

    if (Notification.permission === 'granted') {
      new Notification(title, {
        body,
        icon: '/favicon.ico',
        ...options
      })
    } else if (Notification.permission !== 'denied') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          new Notification(title, {
            body,
            icon: '/favicon.ico',
            ...options
          })
        }
      })
    }
  }

  /**
   * 请求桌面通知权限
   */
  async requestNotificationPermission() {
    if (!('Notification' in window)) {
      return false
    }

    if (Notification.permission === 'granted') {
      return true
    }

    if (Notification.permission !== 'denied') {
      const permission = await Notification.requestPermission()
      return permission === 'granted'
    }

    return false
  }
}

// 创建单例实例
const notificationService = new NotificationService()

export default notificationService
