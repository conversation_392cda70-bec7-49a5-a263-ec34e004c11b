<template>
  <div class="mobile-test-container">
    <h2>FriendList 移动端适配测试</h2>
    
    <div class="test-section">
      <h3>响应式断点测试</h3>
      <div class="breakpoint-info">
        <div class="current-width">当前宽度: {{ windowWidth }}px</div>
        <div class="current-breakpoint">当前断点: {{ currentBreakpoint }}</div>
      </div>
    </div>

    <div class="test-section">
      <h3>触摸设备检测</h3>
      <div class="device-info">
        <div>是否触摸设备: {{ isTouchDevice ? '是' : '否' }}</div>
        <div>设备像素比: {{ devicePixelRatio }}</div>
        <div>用户代理: {{ userAgent }}</div>
      </div>
    </div>

    <div class="test-section">
      <h3>FriendList 组件预览</h3>
      <div class="component-preview">
        <iframe 
          src="/friend" 
          class="preview-frame"
          :style="{ width: previewWidth + 'px', height: '600px' }"
        ></iframe>
      </div>
      
      <div class="preview-controls">
        <button 
          v-for="size in previewSizes" 
          :key="size.name"
          @click="setPreviewSize(size)"
          :class="{ active: previewWidth === size.width }"
          class="size-btn"
        >
          {{ size.name }} ({{ size.width }}px)
        </button>
      </div>
    </div>

    <div class="test-section">
      <h3>CSS 媒体查询测试</h3>
      <div class="media-query-tests">
        <div class="test-item" :class="{ active: matches.mobile }">
          📱 移动端 (max-width: 768px): {{ matches.mobile ? '匹配' : '不匹配' }}
        </div>
        <div class="test-item" :class="{ active: matches.tablet }">
          📟 平板 (max-width: 1024px): {{ matches.tablet ? '匹配' : '不匹配' }}
        </div>
        <div class="test-item" :class="{ active: matches.touch }">
          👆 触摸设备: {{ matches.touch ? '匹配' : '不匹配' }}
        </div>
        <div class="test-item" :class="{ active: matches.landscape }">
          🔄 横屏: {{ matches.landscape ? '匹配' : '不匹配' }}
        </div>
        <div class="test-item" :class="{ active: matches.highDPI }">
          🔍 高分辨率: {{ matches.highDPI ? '匹配' : '不匹配' }}
        </div>

      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FriendListMobileTest',
  
  data() {
    return {
      windowWidth: window.innerWidth,
      previewWidth: 375,
      previewSizes: [
        { name: 'iPhone SE', width: 375 },
        { name: 'iPhone 12', width: 390 },
        { name: 'iPad', width: 768 },
        { name: 'Desktop', width: 1200 }
      ],
      matches: {
        mobile: false,
        tablet: false,
        touch: false,
        landscape: false,
        highDPI: false
      }
    }
  },

  computed: {
    currentBreakpoint() {
      if (this.windowWidth <= 360) return 'xs (≤360px)'
      if (this.windowWidth <= 480) return 'sm (≤480px)'
      if (this.windowWidth <= 576) return 'md (≤576px)'
      if (this.windowWidth <= 768) return 'lg (≤768px)'
      if (this.windowWidth <= 1024) return 'xl (≤1024px)'
      if (this.windowWidth <= 1200) return 'xxl (≤1200px)'
      return 'xxxl (>1200px)'
    },

    isTouchDevice() {
      return 'ontouchstart' in window || navigator.maxTouchPoints > 0
    },

    devicePixelRatio() {
      return window.devicePixelRatio || 1
    },

    userAgent() {
      return navigator.userAgent.substring(0, 50) + '...'
    }
  },

  mounted() {
    this.setupMediaQueries()
    this.setupResizeListener()
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    this.cleanupMediaQueries()
  },

  methods: {
    setupMediaQueries() {
      const queries = {
        mobile: '(max-width: 768px)',
        tablet: '(max-width: 1024px)',
        touch: '(hover: none) and (pointer: coarse)',
        landscape: '(max-width: 768px) and (orientation: landscape)',
        highDPI: '(-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)'
      }

      this.mediaQueries = {}
      
      Object.keys(queries).forEach(key => {
        const mq = window.matchMedia(queries[key])
        this.mediaQueries[key] = mq
        this.matches[key] = mq.matches
        
        mq.addListener((e) => {
          this.matches[key] = e.matches
        })
      })
    },

    cleanupMediaQueries() {
      if (this.mediaQueries) {
        Object.values(this.mediaQueries).forEach(mq => {
          mq.removeListener()
        })
      }
    },

    setupResizeListener() {
      this.handleResize = () => {
        this.windowWidth = window.innerWidth
      }
      window.addEventListener('resize', this.handleResize)
    },

    setPreviewSize(size) {
      this.previewWidth = size.width
    }
  }
}
</script>

<style lang="scss" scoped>
.mobile-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  h2 {
    color: #2d3748;
    margin-bottom: 30px;
    text-align: center;
  }

  h3 {
    color: #4a5568;
    margin-bottom: 15px;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 8px;
  }
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.breakpoint-info, .device-info {
  display: grid;
  gap: 10px;
  font-family: 'Courier New', monospace;
  background: #f7fafc;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #5a67d8;
}

.component-preview {
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 20px;
  background: #f7fafc;
  display: flex;
  justify-content: center;
  padding: 20px;
}

.preview-frame {
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.preview-controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.size-btn {
  padding: 8px 16px;
  border: 2px solid #e2e8f0;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;

  &:hover {
    border-color: #5a67d8;
    background: #f7fafc;
  }

  &.active {
    background: #5a67d8;
    color: white;
    border-color: #5a67d8;
  }
}

.media-query-tests {
  display: grid;
  gap: 12px;
}

.test-item {
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: #f7fafc;
  transition: all 0.3s ease;
  font-family: 'Courier New', monospace;

  &.active {
    border-color: #48bb78;
    background: #f0fff4;
    color: #22543d;
  }
}

// 响应式测试
@media (max-width: 768px) {
  .mobile-test-container {
    padding: 15px;
  }

  .preview-controls {
    flex-direction: column;
  }

  .size-btn {
    width: 100%;
  }

  .component-preview {
    padding: 10px;
  }

  .preview-frame {
    width: 100% !important;
    max-width: 100%;
  }
}
</style>
