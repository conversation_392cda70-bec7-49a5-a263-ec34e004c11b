import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      hideInMenu: true
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: {
      title: '注册',
      hideInMenu: true
    }
  },
  {
    path: '/',
    component: () => import('@/components/Layout/MainLayout.vue'),
    redirect: '/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: {
          title: '仪表盘',
          icon: 'el-icon-s-home'
        }
      },
      {
        path: 'tasks',
        name: 'Tasks',
        component: () => import('@/views/task/TaskList.vue'),
        meta: {
          title: '任务中心',
          icon: 'el-icon-s-order'
        }
      },
      {
        path: 'task/create',
        name: 'TaskCreate',
        component: () => import('@/views/task/TaskCreate.vue'),
        meta: {
          title: '创建任务',
          hideInMenu: true
        }
      },
      {
        path: 'task/:id',
        name: 'TaskDetail',
        component: () => import('@/views/task/TaskDetail.vue'),
        meta: {
          title: '任务详情',
          hideInMenu: true
        }
      },
      {
        path: 'competitions',
        name: 'Competitions',
        component: () => import('@/views/competition/CompetitionList.vue'),
        meta: {
          title: '竞争激励',
          icon: 'el-icon-trophy'
        }
      },
      {
        path: 'competition/create',
        name: 'CompetitionCreate',
        component: () => import('@/views/competition/CompetitionCreate.vue'),
        meta: {
          title: '发起竞争',
          hideInMenu: true
        }
      },
      {
        path: 'competition/:id',
        name: 'CompetitionDetail',
        component: () => import('@/views/competition/CompetitionDetail.vue'),
        meta: {
          title: '竞争详情',
          hideInMenu: true
        }
      },
      {
        path: 'friends',
        name: 'Friends',
        component: () => import('@/views/friend/FriendList.vue'),
        meta: {
          title: '好友管理',
          icon: 'el-icon-user'
        }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/user/Profile.vue'),
        meta: {
          title: '个人中心',
          icon: 'el-icon-s-custom'
        }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/user/Settings.vue'),
        meta: {
          title: '系统设置',
          hideInMenu: true
        }
      },
      {
        path: 'user/:id',
        name: 'UserProfile',
        component: () => import('@/views/user/UserProfile.vue'),
        meta: {
          title: '用户资料',
          hideInMenu: true
        }
      }
    ]
  },
  {
    path: '/test/proxy',
    name: 'ProxyTest',
    component: () => import('@/views/test/ProxyTest.vue'),
    meta: {
      title: '代理测试',
      hideInMenu: true
    }
  },
  {
    path: '/test/router',
    name: 'RouterTest',
    component: () => import('@/views/test/RouterTest.vue'),
    meta: {
      title: '路由测试',
      hideInMenu: true
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      hideInMenu: true
    }
  },
  {
    path: '*',
    redirect: '/404'
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

// 解决Vue Router重复导航错误
const originalPush = VueRouter.prototype.push
const originalReplace = VueRouter.prototype.replace

VueRouter.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) {
    return originalPush.call(this, location, onResolve, onReject)
  }
  return originalPush.call(this, location).catch(err => {
    if (err.name !== 'NavigationDuplicated') {
      throw err
    }
  })
}

VueRouter.prototype.replace = function replace(location, onResolve, onReject) {
  if (onResolve || onReject) {
    return originalReplace.call(this, location, onResolve, onReject)
  }
  return originalReplace.call(this, location).catch(err => {
    if (err.name !== 'NavigationDuplicated') {
      throw err
    }
  })
}

export default router
