package com.jingzhenjili.service;

import com.jingzhenjili.entity.User;

/**
 * 通知服务接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface NotificationService {

    /**
     * 发送好友请求通知
     * 
     * @param fromUser 发送者
     * @param toUser 接收者
     * @param message 消息内容
     */
    void sendFriendRequestNotification(User fromUser, User toUser, String message);

    /**
     * 发送好友请求接受通知
     * 
     * @param fromUser 接受者
     * @param toUser 发送者
     */
    void sendFriendRequestAcceptedNotification(User fromUser, User toUser);

    /**
     * 发送竞争邀请通知
     * 
     * @param fromUser 发送者
     * @param toUser 接收者
     * @param competitionTitle 竞争标题
     */
    void sendCompetitionInviteNotification(User fromUser, User toUser, String competitionTitle);

    /**
     * 发送系统通知
     * 
     * @param toUser 接收者
     * @param title 标题
     * @param message 消息内容
     */
    void sendSystemNotification(User toUser, String title, String message);
}
