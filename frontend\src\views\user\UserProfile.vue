<template>
  <div class="user-profile">
    <div class="profile-header">
      <div class="header-background"></div>
      <div class="profile-content">
        <div class="avatar-section">
          <el-avatar :src="userInfo.avatar" :size="120" class="user-avatar">
            {{ userInfo.nickname ? userInfo.nickname.charAt(0) : 'U' }}
          </el-avatar>
        </div>
        
        <div class="info-section">
          <div class="user-info-header">
            <div class="user-name-area">
              <h1 class="username">{{ userInfo.nickname || userInfo.username }}</h1>
              <span class="user-badge" v-if="userInfo.isVip">
                <i class="el-icon-star-on"></i> VIP会员
              </span>
            </div>
            <div class="action-buttons">
              <el-button 
                v-if="!isFriend && !requestSent && !isCurrentUser" 
                type="primary" 
                @click="sendFriendRequest"
                :loading="requesting"
              >
                <i class="el-icon-plus"></i>
                添加好友
              </el-button>
              <el-button 
                v-else-if="requestSent && !isCurrentUser" 
                disabled
              >
                <i class="el-icon-check"></i>
                已发送请求
              </el-button>
              <el-button 
                v-else-if="isFriend && !isCurrentUser" 
                type="success"
                @click="inviteCompetition"
              >
                <i class="el-icon-trophy"></i>
                邀请竞争
              </el-button>
            </div>
          </div>
          <p class="user-email" v-if="userInfo.email">
            <i class="el-icon-message"></i>
            {{ userInfo.email }}
          </p>
          <p class="user-bio" v-if="userInfo.bio">{{ userInfo.bio }}</p>
        </div>
      </div>
    </div>

    <div class="profile-body">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="stats-card">
            <div slot="header">
              <span>基本信息</span>
            </div>
            <div class="stats-content">
              <div class="stat-item">
                <span class="stat-label">用户名</span>
                <span class="stat-value">{{ userInfo.username }}</span>
              </div>
              <div class="stat-item" v-if="userInfo.nickname">
                <span class="stat-label">昵称</span>
                <span class="stat-value">{{ userInfo.nickname }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">性别</span>
                <span class="stat-value">{{ getGenderText(userInfo.gender) }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">注册时间</span>
                <span class="stat-value">{{ formatDate(userInfo.createdTime) }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card class="stats-card">
            <div slot="header">
              <span>积分统计</span>
            </div>
            <div class="stats-content">
              <div class="stat-item">
                <span class="stat-label">总积分</span>
                <span class="stat-value highlight">{{ userInfo.totalPoints || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">当前积分</span>
                <span class="stat-value">{{ userInfo.currentPoints || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">等级</span>
                <span class="stat-value level">{{ getUserLevel(userInfo) }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card class="stats-card">
            <div slot="header">
              <span>活动统计</span>
            </div>
            <div class="stats-content">
              <div class="stat-item">
                <span class="stat-label">好友数量</span>
                <span class="stat-value">{{ friendCount }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">参与竞争</span>
                <span class="stat-value">{{ competitionCount }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">最后登录</span>
                <span class="stat-value">{{ formatDate(userInfo.lastLoginTime) }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UserProfile',
  
  data() {
    return {
      loading: false,
      requesting: false,
      userInfo: {},
      isFriend: false,
      requestSent: false,
      friendCount: 0,
      competitionCount: 0
    }
  },
  
  computed: {
    userId() {
      return this.$route.params.id
    },
    isCurrentUser() {
      return this.userId == this.$store.getters.userInfo?.id
    }
  },
  
  async created() {
    await this.loadUserInfo()
    if (!this.isCurrentUser) {
      await this.checkFriendStatus()
    }
    await this.loadStats()
  },
  
  methods: {
    async loadUserInfo() {
      try {
        this.loading = true
        const response = await this.$api.user.getUserById(this.userId)
        this.userInfo = response.data
      } catch (error) {
        this.$message.error('获取用户信息失败')
        console.error(error)
        this.$router.push('/404')
      } finally {
        this.loading = false
      }
    },
    
    async checkFriendStatus() {
      try {
        // 检查是否为好友
        const friendResponse = await this.$api.friend.isFriend(this.userId)
        this.isFriend = friendResponse.data
        
        // 检查是否已发送好友请求
        const requestResponse = await this.$api.friend.checkFriendRequestStatus(this.userId)
        this.requestSent = requestResponse.data === 0
      } catch (error) {
        console.error('检查好友状态失败:', error)
      }
    },
    
    async loadStats() {
      try {
        // 获取好友数量
        const friendCountResponse = await this.$api.friend.getFriendCount()
        this.friendCount = friendCountResponse.data || 0
        
        // TODO: 获取竞争数量
        this.competitionCount = 0
      } catch (error) {
        console.error('获取统计信息失败:', error)
      }
    },
    
    async sendFriendRequest() {
      try {
        this.requesting = true
        const requestData = {
          friendId: parseInt(this.userId),
          remark: `来自${this.$store.getters.userInfo?.nickname || this.$store.getters.userInfo?.username || '用户'}的好友请求`
        }
        await this.$api.friend.sendFriendRequest(requestData)
        this.$message.success(`好友请求已发送给 ${this.userInfo.nickname || this.userInfo.username}`)
        this.requestSent = true
      } catch (error) {
        this.$message.error(error.message || '发送请求失败')
        console.error(error)
      } finally {
        this.requesting = false
      }
    },
    
    inviteCompetition() {
      this.$router.push(`/competition/create?friendId=${this.userId}`)
    },
    
    getUserLevel(user) {
      const points = user.totalPoints || 0
      if (points >= 10000) return '大师'
      if (points >= 5000) return '专家'
      if (points >= 1000) return '高手'
      if (points >= 100) return '新手'
      return '初学者'
    },
    
    getGenderText(gender) {
      switch (gender) {
        case 1: return '男'
        case 2: return '女'
        default: return '未知'
      }
    },
    
    formatDate(date) {
      if (!date) return '未知'
      return this.$utils.formatDate(date, 'YYYY-MM-DD')
    }
  }
}
</script>

<style scoped>
.user-profile {
  min-height: 100vh;
  background: #f5f7fa;
}

.profile-header {
  position: relative;
  background: white;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-background {
  height: 200px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.profile-content {
  position: relative;
  padding: 0 40px 40px;
  margin-top: -80px;
}

.avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.user-avatar {
  border: 4px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.info-section {
  text-align: center;
}

.user-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.user-name-area {
  display: flex;
  align-items: center;
  gap: 10px;
}

.username {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.user-badge {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #8b4513;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.user-email, .user-bio {
  color: #606266;
  margin: 8px 0;
}

.profile-body {
  padding: 0 40px;
}

.stats-card {
  margin-bottom: 20px;
}

.stats-content {
  padding: 10px 0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.stat-value {
  color: #303133;
  font-weight: 500;
}

.stat-value.highlight {
  color: #409eff;
  font-size: 18px;
  font-weight: 600;
}

.stat-value.level {
  background: linear-gradient(45deg, #409eff, #36cfc9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
}

@media (max-width: 768px) {
  .profile-content {
    padding: 0 20px 20px;
  }
  
  .profile-body {
    padding: 0 20px;
  }
  
  .user-info-header {
    flex-direction: column;
    gap: 15px;
  }
}
</style>
