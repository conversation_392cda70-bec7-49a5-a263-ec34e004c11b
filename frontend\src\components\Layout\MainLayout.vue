<template>
  <div class="main-layout">
    <!-- 侧边栏 -->
    <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo">
          <i class="el-icon-trophy"></i>
          <span v-show="!sidebarCollapsed" class="logo-text">竞争激励</span>
        </div>
      </div>
      
      <el-menu
        :default-active="$route.path"
        :collapse="sidebarCollapsed"
        :unique-opened="true"
        router
        class="sidebar-menu"
      >
        <el-menu-item index="/dashboard">
          <i class="el-icon-s-home"></i>
          <span slot="title">仪表盘</span>
        </el-menu-item>
        
        <el-menu-item index="/tasks">
          <i class="el-icon-s-order"></i>
          <span slot="title">任务中心</span>
        </el-menu-item>
        
        <el-menu-item index="/competitions">
          <i class="el-icon-trophy"></i>
          <span slot="title">竞争激励</span>
        </el-menu-item>
        
        <el-menu-item index="/friends">
          <i class="el-icon-user"></i>
          <span slot="title">好友管理</span>
        </el-menu-item>
        
        <el-menu-item index="/profile">
          <i class="el-icon-s-custom"></i>
          <span slot="title">个人中心</span>
        </el-menu-item>
      </el-menu>
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 顶部导航栏 -->
      <div class="navbar">
        <div class="navbar-left">
          <el-button
            type="text"
            icon="el-icon-s-fold"
            class="collapse-btn"
            @click="toggleSidebar"
          />
          
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item v-if="$route.meta.title">
              {{ $route.meta.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="navbar-right">
          <!-- 好友请求通知 -->
          <div class="notification-item" @click="goToFriendRequests">
            <el-badge :value="friendRequestCount" :hidden="friendRequestCount === 0" class="friend-request-badge">
              <el-button type="text" class="notification-btn">
                <i class="el-icon-bell"></i>
              </el-button>
            </el-badge>
          </div>

          <!-- 用户信息下拉菜单 -->
          <el-dropdown @command="handleUserCommand">
            <div class="user-info">
              <el-avatar
                :src="userInfo.avatar"
                :size="32"
                class="user-avatar"
              >
                {{ userInfo.nickname ? userInfo.nickname.charAt(0) : 'U' }}
              </el-avatar>
              <span class="user-name">{{ userInfo.nickname || userInfo.username }}</span>
              <i class="el-icon-arrow-down"></i>
            </div>

            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="profile">
                <i class="el-icon-user"></i>
                个人中心
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <i class="el-icon-setting"></i>
                系统设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <i class="el-icon-switch-button"></i>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 页面内容 -->
      <div class="page-content">
        <router-view />
      </div>
    </div>
    
    <!-- 全局加载遮罩 -->
    <div
      v-loading="loading"
      element-loading-text="加载中..."
      element-loading-background="rgba(0, 0, 0, 0.3)"
      class="global-loading"
      v-if="loading"
    ></div>
  </div>
</template>

<script>
import NotificationService from '@/services/NotificationService'

export default {
  name: 'MainLayout',

  data() {
    return {
      friendRequestCount: 0,
      notificationTimer: null
    }
  },

  computed: {
    sidebarCollapsed() {
      return this.$store.getters.sidebarCollapsed
    },

    userInfo() {
      return this.$store.getters['auth/userInfo']
    },

    loading() {
      return this.$store.getters.loading
    }
  },
  
  mounted() {
    // 初始化通知服务
    NotificationService.init(this.$api)

    // 监听好友请求更新
    NotificationService.on('friendRequestsUpdate', this.handleFriendRequestsUpdate)

    // 启动通知轮询
    NotificationService.startPolling()

    // 请求桌面通知权限
    NotificationService.requestNotificationPermission()
  },

  beforeDestroy() {
    // 停止通知服务
    NotificationService.stopPolling()
    NotificationService.off('friendRequestsUpdate', this.handleFriendRequestsUpdate)
  },

  methods: {
    toggleSidebar() {
      this.$store.dispatch('toggleSidebar')
    },

    handleUserCommand(command) {
      switch (command) {
        case 'profile':
          this.$router.push('/profile')
          break
        case 'settings':
          this.$router.push('/settings')
          break
        case 'logout':
          this.handleLogout()
          break
      }
    },

    // 处理好友请求更新
    handleFriendRequestsUpdate(data) {
      const newCount = data.count

      // 如果有新的好友请求，显示通知
      if (newCount > this.friendRequestCount && this.friendRequestCount >= 0) {
        const newRequestCount = newCount - this.friendRequestCount

        // 显示页面通知
        this.$notify({
          title: '新的好友请求',
          message: `您有 ${newRequestCount} 个新的好友请求`,
          type: 'info',
          duration: 5000,
          onClick: () => {
            this.goToFriendRequests()
          }
        })

        // 显示桌面通知
        NotificationService.showDesktopNotification(
          '新的好友请求',
          `您有 ${newRequestCount} 个新的好友请求`,
          {
            tag: 'friend-request',
            requireInteraction: true
          }
        )
      }

      this.friendRequestCount = newCount
    },

    // 跳转到好友请求页面
    goToFriendRequests() {
      this.$router.push('/friends?tab=requests')
    },
    
    handleLogout() {
      this.$confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('auth/logout')
        this.$router.push('/login')
      }).catch(() => {
        // 取消退出
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.main-layout {
  display: flex;
  height: 100vh;
  width: 100vw;
}

.sidebar {
  width: 250px;
  background: #2c3e50;
  transition: width 0.3s ease;
  overflow: hidden;
  
  &.collapsed {
    width: 64px;
  }
  
  .sidebar-header {
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    border-bottom: 1px solid #34495e;
    
    .logo {
      display: flex;
      align-items: center;
      color: white;
      font-size: 18px;
      font-weight: 600;
      
      i {
        font-size: 24px;
        margin-right: 10px;
        color: #3498db;
      }
      
      .logo-text {
        white-space: nowrap;
      }
    }
  }
  
  .sidebar-menu {
    border: none;
    background: transparent;
    
    .el-menu-item {
      color: #bdc3c7;
      
      &:hover {
        background-color: #34495e;
        color: white;
      }
      
      &.is-active {
        background-color: #3498db;
        color: white;
      }
      
      i {
        color: inherit;
      }
    }
  }
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.navbar {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  
  .navbar-left {
    display: flex;
    align-items: center;
    
    .collapse-btn {
      margin-right: 20px;
      font-size: 18px;
    }
  }
  
  .navbar-right {
    display: flex;
    align-items: center;
    gap: 16px;

    .notification-item {
      .notification-btn {
        padding: 8px;
        font-size: 18px;
        color: #606266;
        transition: all 0.3s ease;

        &:hover {
          color: #409eff;
          background-color: #f0f9ff;
        }
      }

      .friend-request-badge {
        ::v-deep .el-badge__content {
          background-color: #f56c6c;
          border: 2px solid white;
          font-size: 12px;
          height: 18px;
          line-height: 14px;
          min-width: 18px;
          padding: 0 4px;
          animation: pulse 2s infinite;
        }
      }
    }

    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 6px;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f5f7fa;
      }

      .user-avatar {
        margin-right: 8px;
      }

      .user-name {
        margin-right: 8px;
        font-size: 14px;
        color: #2c3e50;
      }

      i {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  background: #f5f7fa;
}

.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

// 响应式设计
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    
    &:not(.collapsed) {
      transform: translateX(0);
    }
  }
  
  .main-content {
    width: 100%;
  }
  
  .navbar {
    padding: 0 15px;
    
    .navbar-left {
      .collapse-btn {
        margin-right: 15px;
      }
    }
  }
}
</style>
