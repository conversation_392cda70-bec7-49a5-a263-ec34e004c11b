# 好友请求修复测试指南

## 问题描述
同意他人的好友请求后，只有发起方能看到好友关系，接受方看不到对应好友。

## 修复内容

### 1. 后端修复
- 在 `acceptFriendRequest` 方法中添加了详细的调试日志
- 使用 `baseMapper.insert()` 代替 `save()` 方法创建反向好友关系
- 明确设置 `deleted=0` 确保记录不被逻辑删除
- 添加了事务后的验证逻辑

### 2. 前端修复
- 确保接受好友请求后无论当前在哪个标签页都会刷新好友列表
- 修改了 `acceptRequest` 和批量接受请求的方法

## 测试步骤

### 准备工作
1. 确保有两个测试用户账号（用户A和用户B）
2. 启动后端服务，确保日志级别设置为 DEBUG
3. 启动前端服务

### 测试流程

#### 步骤1：发送好友请求
1. 用户A登录系统
2. 进入好友管理页面 -> 添加好友标签页
3. 搜索用户B的用户名或昵称
4. 点击"添加好友"按钮
5. **预期结果**：显示"好友请求已发送"提示

#### 步骤2：检查请求状态
1. 用户B登录系统
2. 进入好友管理页面 -> 好友请求标签页
3. **预期结果**：能看到来自用户A的好友请求

#### 步骤3：接受好友请求
1. 用户B点击"接受"按钮
2. **预期结果**：显示"已接受好友请求"提示

#### 步骤4：验证双方好友列表
1. 用户B切换到"我的好友"标签页
2. **预期结果**：能看到用户A在好友列表中
3. 用户A刷新页面或重新登录
4. 进入好友管理页面 -> 我的好友标签页
5. **预期结果**：能看到用户B在好友列表中

### 后端日志检查
在接受好友请求时，后端应该输出类似以下的日志：

```
[DEBUG] 查找反向好友关系: userId=2, friendId=1, 找到记录=false
[DEBUG] 创建新的反向好友关系: userId=2, friendId=1
[DEBUG] 创建反向记录结果: true, 新记录ID: 123, 插入行数: 1
[DEBUG] 用户2接受了用户1的好友请求，原记录更新结果: true, 反向记录创建结果: true
[DEBUG] 接受者2的好友数量: 1, 发起者1的好友数量: 1
[DEBUG] 获取用户1的好友列表
[DEBUG] 用户1的好友数量: 1
[DEBUG] 获取用户2的好友列表
[DEBUG] 用户2的好友数量: 1
```

### 数据库验证
可以直接查询数据库验证好友关系：

```sql
-- 查看所有好友关系
SELECT * FROM friendships WHERE deleted = 0 AND status = 1;

-- 查看特定用户的好友关系
SELECT 
    f.id,
    f.user_id,
    f.friend_id,
    f.status,
    f.deleted,
    u1.username as user_name,
    u2.username as friend_name
FROM friendships f
JOIN users u1 ON f.user_id = u1.id
JOIN users u2 ON f.friend_id = u2.id
WHERE f.deleted = 0 AND f.status = 1
ORDER BY f.created_time DESC;
```

## 常见问题排查

### 问题1：后端日志显示创建成功，但前端看不到
- 检查前端是否正确调用了 `loadFriends()` 方法
- 检查浏览器网络请求是否成功
- 清除浏览器缓存重试

### 问题2：数据库中有记录，但查询不到
- 检查 `deleted` 字段是否为 0
- 检查 `status` 字段是否为 1
- 检查 MyBatis-Plus 的逻辑删除配置

### 问题3：创建反向记录失败
- 检查数据库唯一约束是否冲突
- 检查是否有其他事务干扰
- 查看详细的错误日志

## 回滚方案
如果修复出现问题，可以：
1. 回滚到之前的代码版本
2. 或者注释掉新增的调试日志
3. 恢复使用 `save()` 方法而不是 `baseMapper.insert()`
